# Milkdown 表格 BR 标签修复补丁

## 概述
本补丁修复了 Milkdown 表格组件中 BR 标签导致内容溢出到下一个单元格的问题。修复包含两个核心文件的修改，无依赖顺序要求。

## 修复内容

### 1. 硬换行过滤插件逻辑优化
**文件**: `@milkdown/preset-commonmark/src/plugin/hardbreak-filter-plugin.ts`

**主要修改**:
- 从过滤列表中移除 `'table'`，允许表格内使用硬换行
- 增加精确的表格层级检查逻辑
- 允许在表格单元格内插入硬换行，但禁止在表格行级别直接插入硬换行

**关键特性**:
- 支持 `Shift+Enter` 快捷键在表格单元格内插入硬换行
- 智能识别表格单元格和表格行的层级关系
- 保持对代码块的硬换行过滤

### 2. 表格单元格内容支持增强
**文件**: `@milkdown/preset-gfm/src/node/table/schema.ts`

**主要修改**:
- 将 `cellContent` 从 `'paragraph'` 改为 `'paragraph+'`，允许多个段落
- 实现智能内容处理逻辑，根据子节点类型决定是否需要段落包装
- 为表格头部单元格和普通单元格应用相同的处理逻辑

**关键特性**:
- 支持表格单元格内的多段落内容
- 智能处理块级元素和内联元素
- 正确处理空单元格的情况

## 补丁文件
- `patches/@milkdown+preset-commonmark****.3.patch`
- `patches/@milkdown+preset-gfm****.3.patch`

## 安装和使用

### 1. 确保项目中已安装 patch-package
```bash
npm install --save-dev patch-package
```

### 2. 在 package.json 中添加 postinstall 脚本
```json
{
  "scripts": {
    "postinstall": "patch-package"
  }
}
```

### 3. 应用补丁
补丁文件已经创建在 `patches/` 目录中，运行以下命令应用补丁：
```bash
npm install
```

或手动应用：
```bash
npx patch-package
```

## 功能测试

### 测试用例 1：基本 BR 标签
```markdown
| 列1 | 列2 |
|-----|-----|
| 第一行<br>第二行 | 正常内容 |
```

### 测试用例 2：快捷键测试
1. 在表格单元格内输入文本
2. 按 `Shift+Enter`
3. 继续输入文本

### 测试用例 3：多种 BR 格式
```markdown
| 类型 | 内容 |
|------|------|
| BR标签 | 内容1<br/>内容2 |
| BR标签 | 内容A<br>内容B |
| 混合 | 开始<br />中间<br>结束 |
```

## 预期结果
- ✅ 表格单元格内容正确换行，不溢出到相邻单元格
- ✅ 支持 `Shift+Enter` 快捷键插入硬换行
- ✅ 支持所有格式的 BR 标签（`<br>`, `<br/>`, `<br />`）
- ✅ 表格结构保持完整
- ✅ 支持表格单元格内的多段落内容

## 技术细节

### 修复原理
1. **硬换行过滤优化**: 通过精确的节点层级检查，区分表格行和表格单元格，只在单元格内允许硬换行
2. **单元格内容增强**: 支持多段落内容，智能处理不同类型的子节点

### 兼容性
- Milkdown 版本: 7.6.3
- 向后兼容，不影响现有功能
- 不依赖额外的第三方库

## 验证
运行验证脚本确认修复生效：
```bash
node verify-patch.js
```

## 备份文件
原始文件已备份为 `.backup` 后缀，如需回滚可以参考：
- `hardbreak-filter-plugin.ts.backup`
- `schema.ts.backup`
