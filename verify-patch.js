const fs = require('fs');

function verifyFile(filePath, checks, description) {
  console.log(`检查文件: ${filePath}`);
  
  if (!fs.existsSync(filePath)) {
    console.log(`❌ 文件不存在: ${filePath}`);
    return false;
  }
  
  const content = fs.readFileSync(filePath, 'utf8');
  let allPassed = true;
  
  checks.forEach((check, index) => {
    if (content.includes(check)) {
      console.log(`  ✅ 检查 ${index + 1}: 找到预期内容`);
    } else {
      console.log(`  ❌ 检查 ${index + 1}: 未找到预期内容`);
      console.log(`     期望: ${check.substring(0, 50)}...`);
      allPassed = false;
    }
  });
  
  console.log(`${description}: ${allPassed ? '✅ 通过' : '❌ 失败'}\n`);
  return allPassed;
}

// 验证修改
const checks = [
  {
    file: 'node_modules/@milkdown/preset-commonmark/src/plugin/hardbreak-filter-plugin.ts',
    checks: [
      "['code_block']",
      'inTableCell',
      'inTableRow',
      'Special handling for tables'
    ],
    description: '硬换行过滤插件修复'
  },
  {
    file: 'node_modules/@milkdown/preset-gfm/src/node/table/schema.ts',
    checks: [
      "cellContent: 'paragraph+'",
      'hasBlockContent',
      'Handle cell content'
    ],
    description: '表格单元格内容支持增强'
  }
];

let allPassed = true;
checks.forEach(check => {
  if (!verifyFile(check.file, check.checks, check.description)) {
    allPassed = false;
  }
});

console.log(allPassed ? '🎉 所有修改验证通过！' : '❌ 修改验证失败！');
process.exit(allPassed ? 0 : 1);
