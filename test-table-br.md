# 表格 BR 标签测试

## 测试用例 1：基本 BR 标签
| 列1 | 列2 |
|-----|-----|
| 第一行<br>第二行 | 正常内容 |
| 内容A<br/>内容B | 其他内容 |

## 测试用例 2：多种 BR 格式
| 类型 | 内容 |
|------|------|
| BR标签 | 内容1<br/>内容2 |
| BR标签 | 内容A<br>内容B |
| 混合 | 开始<br />中间<br>结束 |

## 测试用例 3：复杂表格内容
| 功能 | 描述 | 示例 |
|------|------|------|
| 硬换行 | 支持在单元格内换行<br>不会溢出到下一个单元格 | 第一行<br>第二行 |
| 多段落 | 支持多个段落内容<br><br>这是第二段 | 段落1<br><br>段落2 |
| 混合内容 | 文本和换行混合<br>**粗体文本**<br>*斜体文本* | 普通<br>**粗体**<br>*斜体* |

## 预期结果
- 所有 BR 标签应该在单元格内正确换行
- 内容不应该溢出到相邻单元格
- 表格结构应该保持完整
- 支持 Shift+Enter 快捷键插入硬换行
