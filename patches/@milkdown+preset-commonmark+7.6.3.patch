diff --git a/node_modules/@milkdown/preset-commonmark/src/plugin/hardbreak-filter-plugin.ts b/node_modules/@milkdown/preset-commonmark/src/plugin/hardbreak-filter-plugin.ts
index 24f8e4f..7e0faa8 100644
--- a/node_modules/@milkdown/preset-commonmark/src/plugin/hardbreak-filter-plugin.ts
+++ b/node_modules/@milkdown/preset-commonmark/src/plugin/hardbreak-filter-plugin.ts
@@ -4,7 +4,7 @@ import { withMeta } from '../__internal__'
 
 /// This slice contains the nodes that within which the hardbreak will be ignored.
 export const hardbreakFilterNodes = $ctx(
-  ['table', 'code_block'],
+  ['code_block'],
   'hardbreakFilterNodes'
 )
 
@@ -15,6 +15,7 @@ withMeta(hardbreakFilterNodes, {
 
 /// This plugin is used to filter the hardbreak node.
 /// If the hardbreak is going to be inserted within a node that is in the `hardbreakFilterNodes`, ignore it.
+/// Special handling for tables: allow hardbreaks in table cells but not in table rows directly.
 export const hardbreakFilterPlugin = $prose((ctx) => {
   const notIn = ctx.get(hardbreakFilterNodes.key)
   return new Plugin({
@@ -27,11 +28,34 @@ export const hardbreakFilterPlugin = $prose((ctx) => {
         const $from = state.doc.resolve(from)
         let curDepth = $from.depth
         let canApply = true
+        let inTableCell = false
+        let inTableRow = false
+
+        // Check the node hierarchy
         while (curDepth > 0) {
-          if (notIn.includes($from.node(curDepth).type.name)) canApply = false
+          const nodeName = $from.node(curDepth).type.name
+
+          // Track if we're in a table cell or table row
+          if (nodeName === 'table_cell' || nodeName === 'table_header') {
+            inTableCell = true
+          }
+          if (nodeName === 'table_row' || nodeName === 'table_header_row') {
+            inTableRow = true
+          }
+
+          // Apply original filter logic for non-table nodes
+          if (notIn.includes(nodeName)) {
+            canApply = false
+          }
 
           curDepth--
         }
+
+        // Special table handling: allow hardbreaks in table cells but not directly in table rows
+        if (inTableRow && !inTableCell) {
+          canApply = false
+        }
+
         return canApply
       }
       return true
diff --git a/node_modules/@milkdown/preset-commonmark/src/plugin/hardbreak-filter-plugin.ts.backup b/node_modules/@milkdown/preset-commonmark/src/plugin/hardbreak-filter-plugin.ts.backup
new file mode 100644
index 0000000..24f8e4f
--- /dev/null
+++ b/node_modules/@milkdown/preset-commonmark/src/plugin/hardbreak-filter-plugin.ts.backup
@@ -0,0 +1,45 @@
+import { Plugin, PluginKey } from '@milkdown/prose/state'
+import { $ctx, $prose } from '@milkdown/utils'
+import { withMeta } from '../__internal__'
+
+/// This slice contains the nodes that within which the hardbreak will be ignored.
+export const hardbreakFilterNodes = $ctx(
+  ['table', 'code_block'],
+  'hardbreakFilterNodes'
+)
+
+withMeta(hardbreakFilterNodes, {
+  displayName: 'Ctx<hardbreakFilterNodes>',
+  group: 'Prose',
+})
+
+/// This plugin is used to filter the hardbreak node.
+/// If the hardbreak is going to be inserted within a node that is in the `hardbreakFilterNodes`, ignore it.
+export const hardbreakFilterPlugin = $prose((ctx) => {
+  const notIn = ctx.get(hardbreakFilterNodes.key)
+  return new Plugin({
+    key: new PluginKey('MILKDOWN_HARDBREAK_FILTER'),
+    filterTransaction: (tr, state) => {
+      const isInsertHr = tr.getMeta('hardbreak')
+      const [step] = tr.steps
+      if (isInsertHr && step) {
+        const { from } = step as unknown as { from: number }
+        const $from = state.doc.resolve(from)
+        let curDepth = $from.depth
+        let canApply = true
+        while (curDepth > 0) {
+          if (notIn.includes($from.node(curDepth).type.name)) canApply = false
+
+          curDepth--
+        }
+        return canApply
+      }
+      return true
+    },
+  })
+})
+
+withMeta(hardbreakFilterPlugin, {
+  displayName: 'Prose<hardbreakFilterPlugin>',
+  group: 'Prose',
+})
